{"name": "zod-staff-admin", "version": "0.1.0", "homepage": "/", "private": true, "dependencies": {"@babel/traverse": "^7.23.2", "@ckeditor/ckeditor5-build-classic": "^40.0.0", "@ckeditor/ckeditor5-react": "^6.0.0", "@emotion/react": "^11.10.6", "@emotion/styled": "^11.10.6", "@fortawesome/fontawesome-free": "^6.4.2", "@fortawesome/free-solid-svg-icons": "^6.4.2", "@fortawesome/react-fontawesome": "^0.2.0", "@fullcalendar/daygrid": "^6.1.9", "@fullcalendar/interaction": "^6.1.9", "@fullcalendar/react": "^6.1.9", "@fullcalendar/timegrid": "^6.1.9", "@hookform/resolvers": "^3.3.2", "@mui/icons-material": "^5.14.18", "@mui/material": "^5.14.18", "@react-jvectormap/core": "^1.0.4", "@react-latest-ui/react-sticky-notes": "^0.2.6", "@tanstack/react-query": "^5.66.9", "@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^14.1.0", "@testing-library/user-event": "^14.5.1", "alertifyjs": "^1.13.1", "antd": "^5.11.1", "apexcharts": "^3.44.0", "axios": "^1.7.9", "bootstrap": "^5.3.2", "feather-icons-react": "^0.6.2", "fs": "^0.0.1-security", "fullcalendar": "^6.1.9", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "lodash": "^4.17.21", "path": "^0.12.7", "prop-types": "^15.8.1", "react": "^18.2.0", "react-apexcharts": "^1.4.1", "react-bootstrap": "^2.9.1", "react-calendar": "^4.6.1", "react-chartjs-2": "^5.2.0", "react-circular-progressbar": "^2.1.0", "react-country-flag": "^3.1.0", "react-countup": "^6.5.0", "react-custom-scrollbars-2": "^4.5.0", "react-data-table-component": "^7.5.4", "react-data-table-component-extensions": "^1.6.0", "react-datepicker": "^4.21.0", "react-datetime-bootstrap": "^1.4.7", "react-donut-chart": "^1.3.3", "react-drag-and-drop": "^3.0.0", "react-dropzone": "^14.2.10", "react-feather": "^2.0.10", "react-full-screen": "^1.1.1", "react-helmet": "^6.1.0", "react-hook-form": "^7.54.2", "react-input-mask": "^2.0.4", "react-modal-image": "^2.6.0", "react-owl-carousel": "^2.3.3", "react-photoswipe-gallery": "^2.2.7", "react-router-dom": "^6.18.0", "react-scripts": "^5.0.1", "react-select": "^5.8.0", "react-slick": "^0.29.0", "react-stars": "^2.2.5", "react-tag-input-component": "^2.0.2", "react-toastify": "^11.0.5", "slick-carousel": "^1.8.1", "source-map-loader": "^4.0.1", "util": "^0.12.5", "weather-icons-react": "^1.2.0", "yet-another-react-lightbox": "^3.15.2"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint .", "lint:fix": "eslint --fix ."}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "overrides": {"semver": "~7.5.2"}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "eslint": "^8.53.0"}}