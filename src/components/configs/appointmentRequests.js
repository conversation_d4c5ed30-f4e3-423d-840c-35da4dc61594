export const appointmentRequets = [
  {
    id: 1,
    patientname: "<PERSON><PERSON>",
    time: "01-12-2024 2:30 pm",
    status: "Pending",
    department: "Cardiology",
    assingned: "<PERSON><PERSON> <PERSON>",
    type: "Fast Tag",
    contactinfo: "+91 69639138",
    link: "",
  },
  {
    id: 2,
    patientname: "<PERSON><PERSON>",
    time: "02-12-2024 10:30 am",
    status: "Pending",
    department: "ENT",
    assingned: "Dr<PERSON><PERSON>",
    type: "Fast Tag",
    contactinfo: "+91 69639138",
    link: "",
  },
  {
    id: 3,
    patientname: "<PERSON><PERSON>",
    time: "03-12-2024 10:30 am",
    status: "Pending",
    department: "Dermetology",
    assingned: "Dr<PERSON>",
    type: "Non Fast Tag",
    contactinfo: "+91 69639138",
    link: "",
  },
  {
    id: 4,
    patientname: "<PERSON><PERSON>",
    time: "03-12-2024 11:30 am",
    status: "Pending",
    department: "Cardiology",
    assingned: "<PERSON><PERSON> <PERSON>",
    type: "Fast Tag",
    contactinfo: "+91 69639138",
    link: "",
  },
  {
    id: 5,
    patientname: "<PERSON><PERSON><PERSON>",
    time: "05-12-2024 12:30 pm",
    status: "Pending",
    department: "Cardiology",
    assingned: "Dr. <PERSON>",
    type: "Fast Tag",
    contactinfo: "+91 69639138",
    link: "",
  },
  {
    id: 6,
    patientname: "Rajesh",
    time: "21-03-2024 10:30 am",
    status: "Pending",
    department: "Cardiology",
    assingned: "Dr. <PERSON>",
    type: "Fast Tag",
    contactinfo: "+91 69639138",
    link: "",
  },
  {
    id: 7,
    patientname: "Rajesh",
    time: "21-03-2024 10:30 am",
    status: "Pending",
    department: "Cardiology",
    assingned: "Dr. Smith",
    type: "Fast Tag",
    contactinfo: "+91 69639138",
    link: "",
  },
  {
    id: 8,
    patientname: "Rajesh",
    time: "21-03-2024 10:30 am",
    status: "Pending",
    department: "Cardiology",
    assingned: "Dr. Smith",
    type: "Fast Tag",
    contactinfo: "+91 69639138",
    link: "",
  },
  {
    id: 9,
    patientname: "Rajesh",
    time: "21-03-2024 10:30 am",
    status: "Pending",
    department: "Cardiology",
    assingned: "Dr. Smith",
    type: "Fast Tag",
    contactinfo: "+91 69639138",
    link: "",
  },
  {
    id: 10,
    patientname: "Rajesh",
    time: "21-03-2024 10:30 am",
    status: "Pending",
    department: "Cardiology",
    assingned: "Dr. Smith",
    type: "Fast Tag",
    contactinfo: "+91 69639138",
    link: "",
  },
  {
    id: 11,
    patientname: "Rajesh",
    time: "21-03-2024 10:30 am",
    status: "Pending",
    department: "Cardiology",
    assingned: "Dr. Smith",
    type: "Fast Tag",
    contactinfo: "+91 69639138",
    link: "",
  },
  {
    id: 12,
    patientname: "Rajesh",
    time: "21-03-2024 10:30 am",
    status: "Pending",
    department: "Cardiology",
    assingned: "Dr. Smith",
    type: "Fast Tag",
    contactinfo: "+91 69639138",
    link: "",
  },
];
