import PropTypes from "prop-types";
import { Link } from "react-router-dom";
import DataTable from "../Tables/DataTable";
import { pdficon, printericon } from "../imagepath";
import DateSearchHero from "../heros/DateSearchHero";
import { formatTime } from "../configs/formatTime";
import CenteredModal from "../modals/CenteredModal";
import { useState } from "react";
import Prescription from "./Prescription";
import jsPDF from "jspdf";

function AppointmentTable(props) {
  const { appointmentList, loading, handleDate, handleSearch } = props;
  console.log("Appointment List: ", appointmentList);
  const [show,setShow] = useState(false);
  const [prescriptionUrl, setPrescriptionUrl] = useState("");
  const handleClose = () => {
    setShow(false);
  };
  const handleView = (url) => {
    // Logic to handle view action
    console.log("View action triggered");
    console.log("Prescription URL: ", url);
    setPrescriptionUrl(url);
    // Open the modal to show the prescription
    setShow(true);
  };

  const generateCaseSheetPDF = (appointment) => {
    const doc = new jsPDF();
    const pageWidth = doc.internal.pageSize.width;
    const pageHeight = doc.internal.pageSize.height;

    // Add logo (if available)
    try {
      // You can add logo here if needed
      // doc.addImage(logo, 'PNG', 10, 10, 30, 15);
    } catch (error) {
      console.log("Logo not added:", error);
    }

    // Header
    doc.setFontSize(20);
    doc.setFont("helvetica", "bold");
    doc.text("CASE SHEET", pageWidth / 2, 30, { align: "center" });

    // Hospital Information
    if (appointment.hospital) {
      doc.setFontSize(14);
      doc.setFont("helvetica", "bold");
      doc.text(appointment.hospital.name, pageWidth / 2, 45, { align: "center" });

      doc.setFontSize(10);
      doc.setFont("helvetica", "normal");
      if (appointment.hospital.address) {
        const address = `${appointment.hospital.address.lineTwo || appointment.hospital.address.lineOne}`;
        doc.text(address, pageWidth / 2, 52, { align: "center" });
      }

      if (appointment.hospital.contact_details) {
        doc.text(`Phone: ${appointment.hospital.contact_details.mobile}`, pageWidth / 2, 58, { align: "center" });
        if (appointment.hospital.contact_details.email) {
          doc.text(`Email: ${appointment.hospital.contact_details.email}`, pageWidth / 2, 64, { align: "center" });
        }
      }
    }

    // Line separator
    doc.setLineWidth(0.5);
    doc.line(10, 75, pageWidth - 10, 75);

    // Patient Information
    let yPosition = 90;
    doc.setFontSize(14);
    doc.setFont("helvetica", "bold");
    doc.text("PATIENT INFORMATION", 10, yPosition);

    yPosition += 10;
    doc.setFontSize(11);
    doc.setFont("helvetica", "normal");

    const patientName = appointment.user_details?.name || appointment.user?.first_name || "N/A";
    doc.text(`Patient Name: ${patientName}`, 10, yPosition);
    yPosition += 8;

    doc.text(`Booking ID: ${appointment.booking_id}`, 10, yPosition);
    yPosition += 8;

    if (appointment.user_details?.age || appointment.user?.age) {
      doc.text(`Age: ${appointment.user_details?.age || appointment.user?.age} years`, 10, yPosition);
      yPosition += 8;
    }

    if (appointment.user_details?.gender || appointment.user?.gender) {
      doc.text(`Gender: ${(appointment.user_details?.gender || appointment.user?.gender).charAt(0).toUpperCase() + (appointment.user_details?.gender || appointment.user?.gender).slice(1)}`, 10, yPosition);
      yPosition += 8;
    }

    if (appointment.user?.phone) {
      doc.text(`Phone: ${appointment.user.phone}`, 10, yPosition);
      yPosition += 8;
    }

    // Appointment Information
    yPosition += 10;
    doc.setFontSize(14);
    doc.setFont("helvetica", "bold");
    doc.text("APPOINTMENT DETAILS", 10, yPosition);

    yPosition += 10;
    doc.setFontSize(11);
    doc.setFont("helvetica", "normal");

    doc.text(`Appointment Date: ${new Date(appointment.appointmentDate).toLocaleDateString()}`, 10, yPosition);
    yPosition += 8;

    if (appointment.timeSlot) {
      doc.text(`Time Slot: ${formatTime(appointment.timeSlot)}`, 10, yPosition);
      yPosition += 8;
    }

    doc.text(`Type: ${appointment.type.charAt(0).toUpperCase() + appointment.type.slice(1)}`, 10, yPosition);
    yPosition += 8;

    doc.text(`Status: ${appointment.status.charAt(0).toUpperCase() + appointment.status.slice(1)}`, 10, yPosition);
    yPosition += 8;

    if (appointment.reason) {
      doc.text(`Reason: ${appointment.reason}`, 10, yPosition);
      yPosition += 8;
    }

    // Doctor Information
    if (appointment.doctor) {
      yPosition += 10;
      doc.setFontSize(14);
      doc.setFont("helvetica", "bold");
      doc.text("DOCTOR INFORMATION", 10, yPosition);

      yPosition += 10;
      doc.setFontSize(11);
      doc.setFont("helvetica", "normal");

      doc.text(`Doctor: Dr. ${appointment.doctor.name}`, 10, yPosition);
      yPosition += 8;

      if (appointment.doctor.email) {
        doc.text(`Email: ${appointment.doctor.email}`, 10, yPosition);
        yPosition += 8;
      }

      if (appointment.doctor.phone_number) {
        doc.text(`Phone: ${appointment.doctor.phone_number}`, 10, yPosition);
        yPosition += 8;
      }
    }

    // Payment Information
    if (appointment.meta_data) {
      yPosition += 10;
      doc.setFontSize(14);
      doc.setFont("helvetica", "bold");
      doc.text("PAYMENT INFORMATION", 10, yPosition);

      yPosition += 10;
      doc.setFontSize(11);
      doc.setFont("helvetica", "normal");

      doc.text(`Consultation Fee: ${appointment.meta_data.currency} ${appointment.meta_data.fee}`, 10, yPosition);
      yPosition += 8;

      if (appointment.meta_data.platform_fee) {
        doc.text(`Platform Fee: ${appointment.meta_data.currency} ${appointment.meta_data.platform_fee}`, 10, yPosition);
        yPosition += 8;
      }

      if (appointment.meta_data.discount > 0) {
        doc.text(`Discount: ${appointment.meta_data.currency} ${appointment.meta_data.discount}`, 10, yPosition);
        yPosition += 8;
      }

      doc.text(`Total Amount: ${appointment.meta_data.currency} ${appointment.meta_data.total}`, 10, yPosition);
      yPosition += 8;
    }

    // Footer
    doc.setFontSize(8);
    doc.setFont("helvetica", "italic");
    doc.text(`Generated on: ${new Date().toLocaleString()}`, 10, pageHeight - 20);

    // Generate unique filename
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5);
    const filename = `CaseSheet_${patientName.replace(/\s+/g, '_')}_${timestamp}.pdf`;

    // Save the PDF
    doc.save(filename);
  };
  const columns = [
    {
      title: "Booking ID",
      dataIndex: "booking_id",
      // sorter: (a, b) => a.bookingid.length - b.bookingid.length,
    },
    {
      title: "Patient Name",
      dataIndex: "patientname",
      // sorter: (a, b) => a.patientname.length - b.patientname.length,
      render: (_item, record) => <div>{record?.user_details?.name ??  record?.user?.first_name}</div>,
    },
    {
      title: "Type",
      dataIndex: "type",
      // sorter: (a, b) => a.type.length - b.type.length,
    },
    {
      title: "Time",
      dataIndex: "timeSlot",
      // sorter: (a, b) => a.time.length - b.time.length,
      render: (_item, record) => <div>{formatTime(record?.timeSlot) ?? "unassigned"}</div>,
    },
    {
      title: "Status",
      dataIndex: "status",
      // sorter: (a, b) => a.status.length - b.status.length,
      render: (item) => (
        <div
          className={`delete-badge ${
            (item === "accepted" && "status-orange")||
            (item === "cancelled" && "status-red") ||
            (item === "started" && "status-orange") ||
            (item === "completed" && "status-green")
          }`}
        >
          {item}
        </div>
      ),
    },
    {
      title: "Department",
      dataIndex: "department",
      // sorter: (a, b) => a.department.length - b.department.length,
      render: (_item, record) => <div>{record?.department}</div>,
    },
    {
      title: "Assigned",
      dataIndex: "assingned",
      // sorter: (a, b) => a.assingned.length - b.assingned.length,
      render: (_item, record) => record?.doctor?.name ? <div>Dr.{record?.doctor?.name}</div> : <div>unassigned</div>,
    },
    {
      title: "Prescription",
      dataIndex: "prescription",
      render: (_item, record) => {
        return (
          record?.prescriptionUrl ?
          <div style={{ display: "flex", gap: 8, paddingLeft: "20px" }}>
            <Link to onClick={()=> handleView(record?.prescriptionUrl)}>View</Link>
            <Link to>
              <img src={pdficon} alt="Pdf Icon" width={17} />
            </Link>
          </div>
          :
          <div style={{ paddingLeft: "25px" }}>N/A</div>
        );
      },
    },
    {
      title: "Actions",
      dataIndex: "actions",
      render: (_item, record) => {
        return (
          <div style={{ display: "flex", gap: 8, paddingLeft: "20px" }}>
            <Link
              to="#"
              onClick={(e) => {
                e.preventDefault();
                generateCaseSheetPDF(record);
              }}
              title="Print Case Sheet"
            >
              <img src={printericon} alt="Print Icon" width={17} />
            </Link>
          </div>
        );
      },
    },
  ];
  return (
    <div>
      <DateSearchHero handleDate={handleDate} handleSearch={handleSearch} />
      <DataTable
        columns={columns}
        dataSource={appointmentList ?? []}
        loading={loading}
      />
      <CenteredModal show={show} handleClose={handleClose}>
        <Prescription prescriptionUrl={prescriptionUrl}/>
      </CenteredModal>
    </div>
  );
}

AppointmentTable.propTypes = {
  appointmentList: PropTypes.node,
  loading: PropTypes.bool,
  handleDate: PropTypes.func,
  handleSearch: PropTypes.func,
};

export default AppointmentTable;
