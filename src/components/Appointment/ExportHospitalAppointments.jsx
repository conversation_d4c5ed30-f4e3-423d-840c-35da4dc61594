import { Link } from "react-router-dom";
import { exporticon } from "../imagepath";
import { useAuth } from "../../hooks/useAuth";
import { useExportHospitalBookings } from "../../hooks/appointments/useExportHospitalBookings";

function ExportHospitalAppointments() {
  const { hospitalId } = useAuth();
  const { data, isLoading } = useExportHospitalBookings(hospitalId);

  const handleDownload = () => {
    if (!data) {
      console.error("No data available for download");
      return;
    }

    const url = window.URL.createObjectURL(data);
    const link = document.createElement("a");
    link.href = url;
    link.download = `hospital_appointments_${
      new Date().toISOString().split("T")[0]
    }.xlsx`;

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  };

  return (
    <div className="form-group local-forms">
      <Link
        to="#"
        className="outlined-btn form-control"
        onClick={(e) => {
          e.preventDefault();
          handleDownload();
        }}
        style={{
          opacity: isLoading ? 0.6 : 1,
          pointerEvents: isLoading ? "none" : "auto",
        }}
      >
        <img src={exporticon} alt="" />
        <span className="ms-2 me-2 text-primary">
          {isLoading ? "Loading..." : "Export"}
        </span>
      </Link>
    </div>
  );
}

export default ExportHospitalAppointments;
