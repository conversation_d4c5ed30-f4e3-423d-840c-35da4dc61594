import jsPDF from "jspdf";

/**
 * Generates a case sheet PDF for an appointment
 * @param {Object} appointment - The appointment object containing all details
 * @returns {void} - Downloads the PDF file
 */
export const generateCaseSheetPDF = (appointment) => {
  const doc = new jsPDF();
  const pageWidth = doc.internal.pageSize.width;
  const pageHeight = doc.internal.pageSize.height;
  
  // Header
  doc.setFontSize(20);
  doc.setFont("helvetica", "bold");
  doc.text("CASE SHEET", pageWidth / 2, 30, { align: "center" });
  
  // Hospital Information
  if (appointment.hospital) {
    doc.setFontSize(14);
    doc.setFont("helvetica", "bold");
    doc.text(appointment.hospital.name, pageWidth / 2, 45, { align: "center" });
    
    doc.setFontSize(10);
    doc.setFont("helvetica", "normal");
    if (appointment.hospital.address) {
      const address = `${appointment.hospital.address.lineTwo || appointment.hospital.address.lineOne}`;
      doc.text(address, pageWidth / 2, 52, { align: "center" });
    }
    
    if (appointment.hospital.contact_details) {
      doc.text(`Phone: ${appointment.hospital.contact_details.mobile}`, pageWidth / 2, 58, { align: "center" });
      if (appointment.hospital.contact_details.email) {
        doc.text(`Email: ${appointment.hospital.contact_details.email}`, pageWidth / 2, 64, { align: "center" });
      }
    }
  }
  
  // Line separator
  doc.setLineWidth(0.5);
  doc.line(10, 75, pageWidth - 10, 75);
  
  // Patient Information
  let yPosition = 90;
  doc.setFontSize(14);
  doc.setFont("helvetica", "bold");
  doc.text("PATIENT INFORMATION", 10, yPosition);
  
  yPosition += 10;
  doc.setFontSize(11);
  doc.setFont("helvetica", "normal");
  
  const patientName = appointment.user_details?.name || appointment.user?.first_name || "N/A";
  doc.text(`Patient Name: ${patientName}`, 10, yPosition);
  yPosition += 8;
  
  doc.text(`Booking ID: ${appointment.booking_id}`, 10, yPosition);
  yPosition += 8;
  
  if (appointment.user_details?.age || appointment.user?.age) {
    doc.text(`Age: ${appointment.user_details?.age || appointment.user?.age} years`, 10, yPosition);
    yPosition += 8;
  }
  
  if (appointment.user_details?.gender || appointment.user?.gender) {
    const gender = appointment.user_details?.gender || appointment.user?.gender;
    const formattedGender = gender ? gender.charAt(0).toUpperCase() + gender.slice(1) : 'N/A';
    doc.text(`Gender: ${formattedGender}`, 10, yPosition);
    yPosition += 8;
  }
  
  if (appointment.user?.phone) {
    doc.text(`Phone: ${appointment.user.phone}`, 10, yPosition);
    yPosition += 8;
  }
  
  // Appointment Information
  yPosition += 10;
  doc.setFontSize(14);
  doc.setFont("helvetica", "bold");
  doc.text("APPOINTMENT DETAILS", 10, yPosition);
  
  yPosition += 10;
  doc.setFontSize(11);
  doc.setFont("helvetica", "normal");
  
  doc.text(`Appointment Date: ${new Date(appointment.appointmentDate).toLocaleDateString()}`, 10, yPosition);
  yPosition += 8;
  
  if (appointment.timeSlot) {
    const formatTime = (timeString) => {
      if (!timeString) return "N/A";
      try {
        const [hours, minutes] = timeString.split(':');
        const hour = parseInt(hours, 10);
        const ampm = hour >= 12 ? 'PM' : 'AM';
        const displayHour = hour % 12 || 12;
        return `${displayHour}:${minutes} ${ampm}`;
      } catch (error) {
        return timeString; // Return original if formatting fails
      }
    };
    doc.text(`Time Slot: ${formatTime(appointment.timeSlot)}`, 10, yPosition);
    yPosition += 8;
  }
  
  const appointmentType = appointment.type ? appointment.type.charAt(0).toUpperCase() + appointment.type.slice(1) : 'N/A';
  doc.text(`Type: ${appointmentType}`, 10, yPosition);
  yPosition += 8;

  const appointmentStatus = appointment.status ? appointment.status.charAt(0).toUpperCase() + appointment.status.slice(1) : 'N/A';
  doc.text(`Status: ${appointmentStatus}`, 10, yPosition);
  yPosition += 8;
  
  if (appointment.reason) {
    doc.text(`Reason: ${appointment.reason}`, 10, yPosition);
    yPosition += 8;
  }
  
  // Doctor Information
  if (appointment.doctor) {
    yPosition += 10;
    doc.setFontSize(14);
    doc.setFont("helvetica", "bold");
    doc.text("DOCTOR INFORMATION", 10, yPosition);
    
    yPosition += 10;
    doc.setFontSize(11);
    doc.setFont("helvetica", "normal");
    
    doc.text(`Doctor: Dr. ${appointment.doctor.name}`, 10, yPosition);
    yPosition += 8;
    
    if (appointment.doctor.email) {
      doc.text(`Email: ${appointment.doctor.email}`, 10, yPosition);
      yPosition += 8;
    }
    
    if (appointment.doctor.phone_number) {
      doc.text(`Phone: ${appointment.doctor.phone_number}`, 10, yPosition);
      yPosition += 8;
    }
  }
  
  // Payment Information
  if (appointment.meta_data) {
    yPosition += 10;
    doc.setFontSize(14);
    doc.setFont("helvetica", "bold");
    doc.text("PAYMENT INFORMATION", 10, yPosition);
    
    yPosition += 10;
    doc.setFontSize(11);
    doc.setFont("helvetica", "normal");
    
    doc.text(`Consultation Fee: ${appointment.meta_data.currency} ${appointment.meta_data.fee}`, 10, yPosition);
    yPosition += 8;
    
    if (appointment.meta_data.platform_fee) {
      doc.text(`Platform Fee: ${appointment.meta_data.currency} ${appointment.meta_data.platform_fee}`, 10, yPosition);
      yPosition += 8;
    }
    
    if (appointment.meta_data.discount > 0) {
      doc.text(`Discount: ${appointment.meta_data.currency} ${appointment.meta_data.discount}`, 10, yPosition);
      yPosition += 8;
    }
    
    doc.text(`Total Amount: ${appointment.meta_data.currency} ${appointment.meta_data.total}`, 10, yPosition);
    yPosition += 8;
  }
  
  // Footer
  doc.setFontSize(8);
  doc.setFont("helvetica", "italic");
  doc.text(`Generated on: ${new Date().toLocaleString()}`, 10, pageHeight - 10);
  
  // Generate unique filename
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5);
  const sanitizedPatientName = patientName.replace(/[^a-zA-Z0-9\s]/g, '').replace(/\s+/g, '_') || 'Patient';
  const filename = `CaseSheet_${sanitizedPatientName}_${timestamp}.pdf`;
  
  // Save the PDF
  doc.save(filename);
};
