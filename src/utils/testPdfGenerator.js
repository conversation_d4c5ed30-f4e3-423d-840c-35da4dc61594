import { generateCaseSheetPDF } from './pdfGenerator';

// Sample appointment data for testing
const sampleAppointment = {
  "id": "b343da50-ebf2-40b3-bbcf-9df851f206a5",
  "booking_id": "AM0001",
  "meta_data": {
    "fee": 300,
    "total": 330,
    "currency": "INR",
    "discount": 0,
    "coupon_id": null,
    "coupon_code": null,
    "platform_fee": 30
  },
  "created_by": "fa8e317f-68d8-4b91-9a72-6de261827693",
  "appointmentDate": "2025-07-05T00:00:00.000Z",
  "completed_at": null,
  "timeSlot": "09:00:00",
  "hospital_id": "db401fe4-a184-477f-ad6c-65a29b6ac410",
  "doctor_id": "594c5b05-e10f-4e0a-af92-48d439c28afb",
  "hospital_service_id": null,
  "user_id": "fa8e317f-68d8-4b91-9a72-6de261827693",
  "child_user_id": null,
  "user_details": {
    "age": 22,
    "name": "Sabarinath ",
    "gender": "male"
  },
  "status": "accepted",
  "prescriptionUrl": null,
  "createdAt": "2025-07-05T15:50:23.000Z",
  "updatedAt": "2025-07-05T15:53:15.000Z",
  "type": "consultation",
  "is_fast_tag": false,
  "reason": "Heavy Neck pain",
  "amount": "330.00",
  "is_online": true,
  "token_number": null,
  "user": {
    "id": "fa8e317f-68d8-4b91-9a72-6de261827693",
    "first_name": "Sabarinath ",
    "last_name": null,
    "user_type": "user",
    "email": null,
    "phone": "+917994474783",
    "password": null,
    "is_active": true,
    "hospital_id": null,
    "district_id": "8a0bf907-7b2c-41b0-bca3-edbca1694c7d",
    "job_title": null,
    "profile_picture": null,
    "otp_secret": null,
    "address": null,
    "created_at": "2025-07-05T15:48:56.000Z",
    "updated_at": "2025-07-06T08:20:05.000Z",
    "deleted_at": null,
    "gender": "male",
    "age": 22
  },
  "hospital": {
    "id": "db401fe4-a184-477f-ad6c-65a29b6ac410",
    "name": "Amrita Hospital",
    "logo": "https://slotit.s3.ap-south-1.amazonaws.com/0d0462dd-3013-41da-9927-92cd8dd038e5-filename-Amrita_Hospital_Kochi.jpg",
    "location": "Kochi",
    "address": {
      "city": "Kochi",
      "state": "Kerala ",
      "street": "Ponekkara",
      "lineOne": "Amritha",
      "lineTwo": "Amrita Hospital, Ponekkara, AIMS P.O, Kochi, Kerala, India - 682 041",
      "pincode": "682 041",
      "district": "Ernakulam"
    },
    "contact_details": {
      "email": "<EMAIL>",
      "mobile": "+91 ************",
      "website": "https://www.amritahospitals.org"
    },
    "billing_address": {
      "city": "Kochi",
      "state": "Kerala ",
      "street": "Ponekkara",
      "lineOne": "Amritha",
      "lineTwo": "Amrita Hospital, Ponekkara, AIMS P.O, Kochi, Kerala, India - 682 041",
      "pincode": "682 041",
      "district": "Ernakulam"
    },
    "gst": "1276490",
    "website": null,
    "ratings": null,
    "feedbacks": null,
    "parent_id": null,
    "fastTag": {
      "count": 5,
      "price": 100,
      "enabled": true
    },
    "departments": null,
    "status": "active",
    "isDisabled": false,
    "isDeactivated": false,
    "district_id": null,
    "is_fast_tag_enabled": null,
    "auto_booking_enabled": false,
    "bank_details": {
      "ifsc": "FDRL1869",
      "upi_id": "",
      "bank_name": "Federal Bank",
      "account_holder": "Amrita LTD",
      "account_number": "FDRL18690100041449"
    },
    "total_rating": "0.00",
    "avg_rating": "0.00",
    "rating_count": 0,
    "created_at": "2025-07-05T14:22:01.000Z"
  },
  "doctor": {
    "id": "594c5b05-e10f-4e0a-af92-48d439c28afb",
    "profile_pic": "https://slotit.s3.ap-south-1.amazonaws.com/af1d6348-6e4f-4fc0-b251-d2b43b42298e-filename-istockphoto-**********-612x612.jpg",
    "name": "James Thomas",
    "about": null,
    "email": "<EMAIL>",
    "city": null,
    "status": "active",
    "registration_details": {
      "council_name": "",
      "registration_number": ""
    },
    "address": null,
    "bank_details": null,
    "hospital_id": "db401fe4-a184-477f-ad6c-65a29b6ac410",
    "user_id": "c7541436-b293-442d-aeaf-c06c187e8e1c",
    "phone_number": "**********",
    "pricing": "300.00",
    "work_start_date": "2025-07-05T00:00:00.000Z",
    "total_rating": "0.00",
    "avg_rating": "0.00",
    "rating_count": 0,
    "consultation_duration": 15,
    "departments": []
  }
};

// Function to test PDF generation
export const testPdfGeneration = () => {
  console.log('Testing PDF generation...');
  generateCaseSheetPDF(sampleAppointment);
  console.log('PDF generation test completed!');
};

// You can call this function in the browser console to test
// testPdfGeneration();
